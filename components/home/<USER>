import React from "react";

const doctors = [
  { name: "<PERSON><PERSON>", specialty: "Cardiology", img: "/doctor1.png", rating: 4.9 },
  { name: "Dr. <PERSON>", specialty: "Orthopedics", img: "/doctor2.png", rating: 4.8 },
  { name: "Dr<PERSON> <PERSON>", specialty: "Neurology", img: "/doctor3.png", rating: 4.7 },
  { name: "<PERSON><PERSON>", specialty: "General Medicine", img: "/doctor4.png", rating: 4.8 },
];

const TopDoctors = () => (
  <section className="w-full py-10 bg-[#F8F9FF] flex flex-col items-center">
    <h3 className="text-xl font-bold mb-6">Meet The Top Rated Doctors</h3>
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 w-full max-w-5xl">
      {doctors.map((doc, idx) => (
        <div key={idx} className="flex flex-col items-center bg-white rounded-xl p-6 shadow">
          <img src={doc.img} alt={doc.name} className="w-24 h-24 rounded-full object-cover mb-4" />
          <span className="font-semibold text-lg mb-1">{doc.name}</span>
          <span className="text-gray-500 text-sm mb-2">{doc.specialty}</span>
          <span className="text-green-600 font-bold">★ {doc.rating}</span>
          <button className="bg-[#04CE78] text-white px-4 py-2 rounded-lg font-semibold mt-2">View Profile</button>
        </div>
      ))}
    </div>
  </section>
);

export default TopDoctors;
