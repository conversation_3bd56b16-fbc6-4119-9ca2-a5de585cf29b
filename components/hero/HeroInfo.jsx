import React from "react";
import data from "@/data/hero.json";

const HeroInfo = () => {
  return (
    <section className="w-full flex flex-col items-center justify-center text-center py-12 md:py-20 bg-transparent">
      <div className="mb-4 flex items-center justify-center gap-2">
        <span className="h-2 w-2 rounded-full bg-[#04CE78] inline-block" />
        <span className="text-[#04CE78] font-semibold tracking-wider text-sm md:text-base uppercase">
          {data.welcome}
        </span>
        <span className="h-2 w-2 rounded-full bg-[#04CE78] inline-block" />
      </div>
      <h1 className="text-3xl md:text-5xl font-extrabold text-white mb-4 max-w-3xl">
        {data.title}
      </h1>
      <p className="text-lg md:text-xl text-white/80 max-w-2xl mx-auto">
        {data.subtitle}
      </p>
    </section>
  );
};

export default HeroInfo;