"use client";

import React, { useState } from "react";
import filters from "@/data/filters.json";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { ArrowRightIcon } from "lucide-react";

const FilterBar = () => {
  const [activeToggle, setActiveToggle] = useState(filters.toggles[0].value);
  const [selected, setSelected] = useState({});

  return (
<div className="w-full flex flex-col items-center px-2 md:px-0 -mt-6 z-10 relative">
  <div className="bg-white rounded-2xl shadow-2xl flex flex-col md:flex-row items-center justify-center gap-4 py-8 px-2 md:px-10 w-full border border-gray-100 relative">
    {/* Toggle - now absolutely positioned and centered */}
    <div className="flex gap-2 bg-white rounded-xl p-2 shadow-md absolute -top-8 left-1/2 transform -translate-x-1/2 z-20">
      {filters.toggles.map((toggle) => (
        <button
          key={toggle.value}
          className={`px-6 py-2 rounded-lg font-semibold border transition-colors text-base shadow-sm focus:outline-none focus:ring-2 focus:ring-[#04CE78] ${
            activeToggle === toggle.value
              ? "bg-[#04CE78] text-white border-transparent"
              : "bg-white text-[#1a0856] border-gray-300"
          }`}
          onClick={() => setActiveToggle(toggle.value)}
          type="button"
        >
          {toggle.label}
        </button>
      ))}
    </div>
    
    {/* Filters */}
    <div className="flex flex-1 gap-2 flex-col md:flex-row w-full md:w-auto mt-4 md:mt-0">
      {filters.filters.map((filter) => (
        <Select
          key={filter.label}
          value={selected[filter.label] || ""}
          onValueChange={(val) => setSelected((prev) => ({ ...prev, [filter.label]: val }))}
        >
          <SelectTrigger className="w-full md:w-48 bg-gray-100 text-gray-700 rounded-lg border border-gray-200 shadow-sm focus:ring-2 focus:ring-[#04CE78]">
            <SelectValue placeholder={filter.placeholder} />
          </SelectTrigger>
          <SelectContent>
            {filter.options.map((option) => (
              <SelectItem key={option} value={option} className="text-base">
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ))}
    </div>
    
    {/* Actions */}
    <div className="flex gap-2 w-full md:w-auto mt-2 md:mt-0 flex-col md:flex-row">
      {filters.actions.map((action) => (
        <Button
          key={action.label}
          asChild
          variant={action.variant === "primary" ? "default" : "secondary"}
          className={`font-bold px-6 py-2 rounded-lg shadow flex items-center gap-2 text-base w-full md:w-auto ${
            action.variant === "primary"
              ? "bg-green-500 hover:bg-green-600 text-white"
              : "bg-[#6C2BD7] hover:bg-[#4B1B9B] text-white"
          }`}
        >
          <a href={action.href} className="flex items-center justify-center w-full">
            {action.label} <ArrowRightIcon className="w-5 h-5" />
          </a>
        </Button>
      ))}
    </div>
  </div>
</div>
  );
};

export default FilterBar;
