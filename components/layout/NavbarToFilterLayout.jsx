import React from "react";
import Navbar from "@/components/navbar/Navbar";
import HeroInfo from "@/components/hero/HeroInfo";
import FilterBar from "@/components/hero/FilterBar";

const NavbarToFilterLayout = () => {
  return (
    <div
      className="w-full bg-contain bg-no-repeat bg-center rounded-t-2xl overflow-hidden"
      style={{ backgroundImage: "url(/backgroundImg.png)" }}
    >
      <Navbar />
      <HeroInfo />
      <FilterBar />
    </div>
  );
};

export default NavbarToFilterLayout;
